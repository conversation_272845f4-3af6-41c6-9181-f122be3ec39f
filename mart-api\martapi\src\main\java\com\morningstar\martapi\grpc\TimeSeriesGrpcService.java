package com.morningstar.martapi.grpc;

import com.morningstar.martapi.exception.TsCacheProtobufValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;

import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.util.JsonUtils;
import com.morningstar.martgateway.util.JwtUtil;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import reactor.core.publisher.Mono;

import java.util.ArrayList;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.martapi.config.Constant.LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.*;

@GrpcService
public class TimeSeriesGrpcService extends TimeSeriesServiceGrpc.TimeSeriesServiceImplBase {

    private final MartGateway<TSResponse, MartRequest> tsOldRspGateway;
    private final RequestValidationHandler<HeadersAndParams, MartRequest> validator;
    
    private static final String REQUEST_TYPE_VALUE = "tscache_grpc";

    @Autowired
    public TimeSeriesGrpcService(
            @Qualifier("tsOldRspGateway") MartGateway<TSResponse, MartRequest> tsOldRspGateway,
            @Qualifier("timeSeriesApiValidator") RequestValidationHandler<HeadersAndParams, MartRequest> validator) {
        this.tsOldRspGateway = tsOldRspGateway;
        this.validator = validator;
    }

    @Override
    public void getTimeSeriesData(TimeSeriesRequest request, 
                                  StreamObserver<TsCacheDataForProtoBuf.TimeSeriesDatas> responseObserver) {
        
        String reqId = StringUtils.isNotEmpty(request.getRequestId()) ? request.getRequestId() : UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();

        try {
            // Convert gRPC request to internal format
            MartRequest martRequest = convertToMartRequest(request, reqId);
            HeadersAndParams headersAndParams = convertToHeadersAndParams(request);

            // Validate request
            //validateRequest(martRequest, headersAndParams);

            // Process request
            Mono<TsCacheDataForProtoBuf.TimeSeriesDatas> response = tsOldRspGateway
                    .asyncRetrieveSecurities(martRequest)
                    .map(TSResponse::toProtobuf)
                    .doOnNext(result -> logSuccess(martRequest, reqId, startTime))
                    .doOnError(error -> logError(martRequest, reqId, startTime, error));

            // Subscribe and handle response
            response.subscribe(
                    result -> {
                        responseObserver.onNext(result);
                        responseObserver.onCompleted();
                    },
                    error -> {
                        responseObserver.onError(error);
                    }
            );

        } catch (Exception e) {
            logError(null, reqId, startTime, e);
            responseObserver.onError(e);
        }
    }

    private MartRequest convertToMartRequest(TimeSeriesRequest grpcRequest, String reqId) {
        String userId = getUserIdFromToken(grpcRequest.getUserId(), grpcRequest.getAuthorization());
        String configId = getConfigIdFromToken(grpcRequest.getAuthorization());

        List<String> investmentIds = grpcRequest.getInvestmentIdsList();
        List<String> dataPoints = grpcRequest.getDataPointsList();

        return MartRequest.builder()
                .currency(grpcRequest.getCurrency())
                .dps(dataPoints)
                .ids(investmentIds)
                .startDate(grpcRequest.getStartDate())
                .endDate(grpcRequest.getEndDate())
                .preCurrency(grpcRequest.getPreCurrency())
                .readCache(grpcRequest.getReadCache())
                .productId(grpcRequest.getProductId())
                .entitlementProductId(grpcRequest.getEntitlementProductId())
                .requestId(reqId)
                .userId(userId)
                .dateFormat(grpcRequest.getDateFormat())
                .decimalFormat(grpcRequest.getDecimalFormat())
                .extendedPerformance(grpcRequest.getExtendPerformance())
                .postTax(grpcRequest.getPostTax())
                .useRequireId(grpcRequest.getUseRequireId())
                .checkEntitlement(grpcRequest.getCheckEntitlement())
                .useCase(grpcRequest.getUseCase())
                .useNewCCS(grpcRequest.getUseNewCcs())
                .configId(configId)
                .build();
    }

    private HeadersAndParams convertToHeadersAndParams(TimeSeriesRequest grpcRequest) {
        return HeadersAndParams.builder()
                .authorizationToken(grpcRequest.getAuthorization())
                .productId(grpcRequest.getProductId())
                .requestId(grpcRequest.getRequestId())
                .build();
    }

    private void validateRequest(MartRequest martRequest, HeadersAndParams headersAndParams) {
        try {
            validator.validateHeadersAndParams(headersAndParams);
            validator.validateRequestBody(martRequest);
        } catch (ValidationException e) {
            throw new TsCacheProtobufValidationException(e.getStatus());
        }
    }

    private static String getUserIdFromToken(String headerUserId, String token) {
        return StringUtils.defaultIfEmpty(JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id"), headerUserId);
    }

    private static String getConfigIdFromToken(String token) {
        return JwtUtil.getFieldValue(token, "https://morningstar.com/config_id");
    }

    private void logSuccess(MartRequest martRequest, String reqId, long startTime) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);
        long executionTime = System.currentTimeMillis() - startTime;
        List<LogEntity> logEntities = Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(PRODUCT_ID, martRequest.getProductId()),
                new LogEntity(USER_ID, martRequest.getUserId()),
                new LogEntity(REQUEST_PARAM, "gRPC request"),
                new LogEntity(EXECUTE_TIME, executionTime)
        ).collect(Collectors.toCollection(ArrayList::new));
        addRequestPayload(martRequest, executionTime, logEntities);
        LogEntry.info(logEntities.toArray(LogEntity[]::new));
    }

    private void logError(MartRequest martRequest, String reqId, long startTime, Throwable error) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);
        LogEntry.error(
                new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                new LogEntity(EVENT_DESCRIPTION, error),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(REQUEST_PAYLOAD, martRequest != null ? JsonUtils.toJsonString(martRequest) : "null"),
                new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                new LogEntity(EXCEPTION_TYPE, error.getClass()),
                new LogEntity(PRODUCT_ID, martRequest != null ? martRequest.getProductId() : "unknown"),
                new LogEntity(USER_ID, martRequest != null ? martRequest.getUserId() : "unknown"),
                new LogEntity(REQUEST_PARAM, "gRPC request")
        );
    }

    private void addRequestPayload(MartRequest martRequest, long executionTime, List<LogEntity> logEntities) {
        if (executionTime > LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS) {
            logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)));
        }
    }
}
