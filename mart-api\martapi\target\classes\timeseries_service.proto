syntax = "proto3";

package timeseries;

import "TsCacheData.proto";

option java_multiple_files = true;
option java_package = "com.morningstar.martapi.grpc";
option java_outer_classname = "TimeSeriesServiceProto";

// Time Series Service Definition
service TimeSeriesService {
  // Retrieve time series data as protobuf
  rpc GetTimeSeriesData(TimeSeriesRequest) returns (protobuf.TimeSeriesDatas);
}

// Request message for time series data
message TimeSeriesRequest {
  repeated string investment_ids = 1;
  repeated string data_points = 2;
  string start_date = 3;
  string end_date = 4;
  string currency = 5;
  string pre_currency = 6;
  string read_cache = 7;
  string date_format = 8;
  string decimal_format = 9;
  string extend_performance = 10;
  string post_tax = 11;
  bool use_require_id = 12;
  string use_case = 13;
  bool use_new_ccs = 14;
  string product_id = 15;
  string request_id = 16;
  string trace_id = 17;
  string user_id = 18;
  string authorization = 19;
  bool check_entitlement = 20;
  string entitlement_product_id = 21;
}
