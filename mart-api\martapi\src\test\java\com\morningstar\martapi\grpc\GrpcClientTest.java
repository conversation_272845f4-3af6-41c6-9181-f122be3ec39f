package com.morningstar.martapi.grpc;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;

/**
 * Manual test client for gRPC service.
 * This test is disabled by default as it requires the application to be running.
 * 
 * To use:
 * 1. Start the mart-api application
 * 2. Remove @Disabled annotation
 * 3. Run this test
 */
@Disabled("Requires running application")
public class GrpcClientTest {

    @Test
    void testGrpcConnection() {
        // Create a channel to connect to the server
        ManagedChannel channel = ManagedChannelBuilder.forAddress("localhost", 9090)
                .usePlaintext() // Use plaintext connection (no TLS)
                .build();

        try {
            // Create a blocking stub
            TimeSeriesServiceGrpc.TimeSeriesServiceBlockingStub stub = 
                TimeSeriesServiceGrpc.newBlockingStub(channel);

            // Create a request
            TimeSeriesRequest request = TimeSeriesRequest.newBuilder()
                    .addInvestmentIds("FOUSA00DOU")
                    .addDataPoints("3821")
                    .setStartDate("2023-01-01")
                    .setEndDate("2023-12-31")
                    .setRequestId("test-request-123")
                    .setUserId("test-user")
                    .setAuthorization("Bearer test-token")
                    .setProductId("test-product")
                    .build();

            // Make the call
            System.out.println("Making gRPC call...");
            var response = stub.getTimeSeriesData(request);
            System.out.println("Response received: " + response);

        } catch (Exception e) {
            System.err.println("Error calling gRPC service: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // Shutdown the channel
            channel.shutdown();
        }
    }
}
